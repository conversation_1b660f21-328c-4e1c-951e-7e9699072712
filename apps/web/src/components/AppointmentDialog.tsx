import { useState } from "react";
import { Project, AppointmentType, APPOINTMENT_TYPE_LABELS } from "@/types/project";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { formatDateToISO } from "@/lib/datetime";

interface AppointmentDialogProps {
  project: Project;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (projectId: string, appointment: AppointmentType | null, appointmentDate: string | null) => void;
}

export const AppointmentDialog = ({
  project,
  open,
  onOpenChange,
  onSave,
}: AppointmentDialogProps) => {
  const [appointment, setAppointment] = useState<AppointmentType | "none">(
    project.appointment || "none"
  );
  const [appointmentDate, setAppointmentDate] = useState<string>(
    project.appointmentDate ? formatDateToISO(project.appointmentDate) : ""
  );

  const handleSave = () => {
    onSave(
      project.id,
      appointment === "none" ? null : appointment,
      appointmentDate || null
    );
    onOpenChange(false);
  };

  const handleCancel = () => {
    // Reset to original values
    setAppointment(project.appointment || "none");
    setAppointmentDate(
      project.appointmentDate ? formatDateToISO(project.appointmentDate) : ""
    );
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Update Appointment Details</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="project-info" className="text-sm font-medium">
              Project
            </Label>
            <div className="text-sm text-muted-foreground">
              {project.title} - {project.client}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="appointment-type" className="text-sm font-medium">
              Appointment Type
            </Label>
            <Select
              value={appointment}
              onValueChange={(value: AppointmentType | "none") => setAppointment(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select appointment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No appointment</SelectItem>
                {Object.entries(APPOINTMENT_TYPE_LABELS).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="appointment-date" className="text-sm font-medium">
              Appointment Date
            </Label>
            <Input
              id="appointment-date"
              type="date"
              value={appointmentDate}
              onChange={(e) => setAppointmentDate(e.target.value)}
              disabled={appointment === "none"}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

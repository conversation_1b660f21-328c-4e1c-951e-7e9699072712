import { useState } from 'react';
import { SupervisorStatus, SupervisorSubTask, SUPERVISOR_SUBTASK_LABELS } from '@/types/project';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface SupervisorProgressDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentStatus: SupervisorStatus;
  supervisorSelectedPhases?: string[];
  supervisorPhaseDates?: Record<string, string>;
  onConfirm: (selectedPhase: SupervisorSubTask, completionDate: string) => void;
}

export function SupervisorProgressDialog({
  open,
  onOpenChange,
  currentStatus,
  supervisorSelectedPhases = [],
  supervisorPhaseDates = {},
  onConfirm,
}: SupervisorProgressDialogProps) {
  const [selectedPhase, setSelectedPhase] = useState<SupervisorSubTask | ''>('');
  const [completionDate, setCompletionDate] = useState<Date | undefined>(new Date());
  const [showCalendar, setShowCalendar] = useState(false);

  // Get available phases (subtasks) that can be completed (tolerate nulls from API)
  const getAvailablePhases = (): SupervisorSubTask[] => {
    const selected = (supervisorSelectedPhases as SupervisorSubTask[]) || [];
    const dates = (supervisorPhaseDates as Record<string, string>) || {};
    // Show only selected subtasks that haven't been completed yet
    return selected.filter(phase => !dates[phase]);
  };

  const availablePhases = getAvailablePhases();

  const handleConfirm = () => {
    if (selectedPhase !== '' && completionDate) {
      const dateString = completionDate.toISOString();
      onConfirm(selectedPhase, dateString);
      onOpenChange(false);
      setSelectedPhase('');
      setCompletionDate(new Date());
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
    setSelectedPhase('');
    setCompletionDate(new Date());
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Complete Supervisor Task</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="phase">Select Task to Complete</Label>
            <Select value={selectedPhase} onValueChange={(value) => setSelectedPhase(value as SupervisorSubTask)}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a task to complete" />
              </SelectTrigger>
              <SelectContent>
                {availablePhases.map((phase) => (
                  <SelectItem key={phase} value={phase}>
                    {SUPERVISOR_SUBTASK_LABELS[phase]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Completion Date</Label>
            <Popover open={showCalendar} onOpenChange={setShowCalendar}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !completionDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {completionDate ? format(completionDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={completionDate}
                  onSelect={(date) => {
                    setCompletionDate(date);
                    setShowCalendar(false);
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedPhase || !completionDate}
          >
            Complete Task
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

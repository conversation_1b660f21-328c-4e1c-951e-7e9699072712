import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Users, UserCheck, Settings } from 'lucide-react';
import RoleSelector from '@/components/RoleSelector';

interface User {
  id: string;
  clerkUserId: string;
  name: string;
  email: string;
  role: 'sales' | 'designer' | 'supervisor' | 'manager' | 'admin' | 'owner';
  createdAt: string;
  updatedAt: string;
}

const ROLE_COLORS = {
  sales: 'bg-blue-100 text-blue-800',
  designer: 'bg-purple-100 text-purple-800',
  supervisor: 'bg-green-100 text-green-800',
  manager: 'bg-red-100 text-red-800',
  admin: 'bg-orange-100 text-orange-800',
};

const ROLE_LABELS = {
  sales: 'Sales',
  designer: 'Designer',
  supervisor: 'Supervisor',
  manager: 'Manager',
  admin: 'Admin',
};

const UserManagement = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [updatingUser, setUpdatingUser] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else if (response.status === 401) {
        toast({
          title: 'Authentication Required',
          description: 'Please sign in to access user management',
          variant: 'destructive',
        });
      } else if (response.status === 403) {
        toast({
          title: 'Access Denied',
          description: 'You need manager permissions to access user management',
          variant: 'destructive',
        });
      } else if (response.status >= 500) {
        toast({
          title: 'Server Error',
          description: 'Server error occurred. Please try again later.',
          variant: 'destructive',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }
    } catch (error) {
      console.error('Error fetching users:', error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast({
          title: 'Network Error',
          description: 'Please check your connection and try again',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to load users',
          variant: 'destructive',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const updateUserRole = async (userId: string, newRole: string) => {
    setUpdatingUser(userId);
    try {
      const response = await fetch(`/api/users/${userId}/role`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ role: newRole }),
      });

      if (response.ok) {
        const updatedUser = await response.json();
        setUsers(users.map(user =>
          user.id === userId ? updatedUser : user
        ));
        toast({
          title: 'Success',
          description: `User role updated to ${ROLE_LABELS[newRole as keyof typeof ROLE_LABELS]}`,
        });
      } else if (response.status === 401) {
        toast({
          title: 'Authentication Required',
          description: 'Please sign in to update user roles',
          variant: 'destructive',
        });
      } else if (response.status === 403) {
        toast({
          title: 'Access Denied',
          description: 'You need manager permissions to update user roles',
          variant: 'destructive',
        });
      } else if (response.status === 404) {
        toast({
          title: 'User Not Found',
          description: 'The user you are trying to update does not exist',
          variant: 'destructive',
        });
      } else if (response.status >= 500) {
        toast({
          title: 'Server Error',
          description: 'Server error occurred. Please try again later.',
          variant: 'destructive',
        });
      } else {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Request failed with status ${response.status}`);
      }
    } catch (error) {
      console.error('Error updating user role:', error);

      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast({
          title: 'Network Error',
          description: 'Please check your connection and try again',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to update user role',
          variant: 'destructive',
        });
      }
    } finally {
      setUpdatingUser(null);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading users...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <Users className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
        </div>
        <p className="text-gray-600">Manage user roles and permissions</p>
      </div>

      <div className="grid gap-6">
        {users.map((user) => (
          <Card key={user.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <UserCheck className="h-5 w-5 text-gray-500" />
                  <div>
                    <CardTitle className="text-lg">{user.name}</CardTitle>
                    <p className="text-sm text-gray-600">{user.email}</p>
                  </div>
                </div>
                <RoleSelector
                  currentRole={user.role}
                  onRoleChange={() => {}} // Read-only in header
                  showBadge={true}
                />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  <p>User ID: {user.id}</p>
                  <p>Joined: {new Date(user.createdAt).toLocaleDateString()}</p>
                  <p>Last Updated: {new Date(user.updatedAt).toLocaleDateString()}</p>
                </div>
                <div className="flex items-center gap-3">
                  <Settings className="h-4 w-4 text-gray-400" />
                  <RoleSelector
                    currentRole={user.role}
                    onRoleChange={(newRole) => updateUserRole(user.id, newRole)}
                    disabled={updatingUser === user.id}
                    size="md"
                  />
                  {updatingUser === user.id && (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {users.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p className="text-gray-600">Users will appear here once they sign up.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UserManagement;

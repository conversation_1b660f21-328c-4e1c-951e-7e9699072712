import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { requireAuth, requireManager, requireOwner, requireOwnerOrManager, AuthenticatedRequest } from '../lib/auth';
import { UserService } from '../lib/userService';

const router = Router();
const prisma = new PrismaClient();

// Validation schemas
const updateRoleSchema = z.object({
  role: z.enum(['sales', 'designer', 'supervisor', 'manager', 'admin', 'owner']),
});

const updateUserSchema = z.object({
  name: z.string().min(1).optional(),
  role: z.enum(['sales', 'designer', 'supervisor', 'manager', 'admin', 'owner']).optional(),
});

// Get all users (owners, managers, and supervisors can access for assignee display)
router.get('/', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    const userRole = req.user?.role;

    // Only owners, managers, and supervisors can access user list
    if (!userRole || !['owner', 'manager', 'supervisor'].includes(userRole)) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const users = await UserService.getAllUsers();

    // For supervisors, only return basic info needed for assignee display
    if (userRole === 'supervisor') {
      const basicUsers = users.map(user => ({
        id: user.id,
        name: user.name,
        role: user.role
      }));
      return res.json(basicUsers);
    }

    // Owners and managers get full user data
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Get current user info
router.get('/me', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    if (!req.user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(req.user);
  } catch (error) {
    console.error('Error fetching current user:', error);
    res.status(500).json({ error: 'Failed to fetch user info' });
  }
});

// Get user by ID
router.get('/:id', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    const user = await UserService.findById(id);
    
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Only owners and managers can view other users' details, others can only view their own
    if (req.user?.role !== 'owner' && req.user?.role !== 'manager' && req.user?.id !== id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Failed to fetch user' });
  }
});

// Update user role (owners only)
router.patch('/:id/role', requireAuth, requireOwner, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    const parse = updateRoleSchema.safeParse(req.body);
    
    if (!parse.success) {
      return res.status(400).json({ error: parse.error.flatten() });
    }
    
    const { role } = parse.data;
    
    // Find user by internal ID
    const targetUser = await UserService.findById(id);
    if (!targetUser) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Update the user's role
    const updatedUser = await UserService.updateUserRole(
      targetUser.clerkUserId,
      role,
      req.clerkUserId!
    );
    
    res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user role:', error);
    if (error instanceof Error && error.message === 'Only managers can update user roles') {
      return res.status(403).json({ error: error.message });
    }
    res.status(500).json({ error: 'Failed to update user role' });
  }
});

// Update user info (users can update their own info, owners and managers can update anyone's)
router.patch('/:id', requireAuth, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    const parse = updateUserSchema.safeParse(req.body);

    if (!parse.success) {
      return res.status(400).json({ error: parse.error.flatten() });
    }

    const updates = parse.data;

    // Find user by internal ID
    const targetUser = await UserService.findById(id);
    if (!targetUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check permissions
    const isOwner = req.user?.role === 'owner';
    const isManager = req.user?.role === 'manager';
    const isOwnProfile = req.user?.id === id;

    if (!isOwner && !isManager && !isOwnProfile) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Only owners can update roles
    if (updates.role && !isOwner) {
      return res.status(403).json({ error: 'Only owners can update roles' });
    }
    
    // Update the user
    let updatedUser;
    if (updates.role && isOwner) {
      updatedUser = await UserService.updateUserRole(
        targetUser.clerkUserId,
        updates.role,
        req.clerkUserId!
      );
      
      // Update other fields if provided
      if (updates.name) {
        updatedUser = await UserService.updateUser(targetUser.clerkUserId, {
          name: updates.name
        });
      }
    } else {
      updatedUser = await UserService.updateUser(targetUser.clerkUserId, updates);
    }
    
    res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    if (error instanceof Error && error.message === 'Only managers can update user roles') {
      return res.status(403).json({ error: error.message });
    }
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Sync user from Clerk (useful for manual sync)
router.post('/:id/sync', requireAuth, requireOwnerOrManager, async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    
    // Find user by internal ID
    const targetUser = await UserService.findById(id);
    if (!targetUser) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    const syncedUser = await UserService.syncUserFromClerk(targetUser.clerkUserId);
    res.json(syncedUser);
  } catch (error) {
    console.error('Error syncing user:', error);
    res.status(500).json({ error: 'Failed to sync user' });
  }
});

export default router;
